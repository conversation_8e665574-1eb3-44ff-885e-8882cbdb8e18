'use client';

import React, { useState, useCallback } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSession } from 'next-auth/react';
import { Building2, MapPin, Phone, Mail, FileText, Palette, Upload } from 'lucide-react';
import { handleCreateSchoolAction, updateMySchool } from '@/actions/school.action';
import { handleCreateBrandAction, handleUpdateBrandAction } from '@/actions/brand.action';
import { handleFileUploadAction } from '@/actions/file.action';
import { createSchoolFormSchema } from '@/lib/validators/school.validator';
import { Button } from '@/components/atoms/Button/Button';
import { FileUpload } from '@/components/molecules/FileUpload/FileUpload';
import Icon from '@/components/atoms/Icon/Icon';
import { cn } from '@/utils/cn';
import { z } from 'zod';

// Extended form schema that includes brand fields (no longer URL-based, using file uploads)
const extendedSchoolFormSchema = createSchoolFormSchema.extend({
  // Brand fields - now just for color since files are handled separately
  color: z.string().regex(/^#([0-9A-Fa-f]{3}){1,2}$/, { message: 'Invalid color hex code' }).optional().or(z.literal('')),
});

type ExtendedSchoolFormData = z.infer<typeof extendedSchoolFormSchema>;

// File validation constants
const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];
const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB

interface IntegratedSchoolSetupProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  className?: string;
  mode?: 'create' | 'edit';
  initialData?: {
    name?: string;
    address?: string;
    phoneNumber?: string;
    email?: string;
    registeredNumber?: string;
    // Brand data
    brand?: {
      id?: string;
      logo?: string;
      color?: string;
      image?: string;
    };
  };
}

export const IntegratedSchoolSetup: React.FC<IntegratedSchoolSetupProps> = ({
  onSuccess,
  onError,
  className,
  mode = 'create',
  initialData
}) => {
  const { update: updateSession } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // File upload states
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(initialData?.brand?.logo || null);
  const [logoUploading, setLogoUploading] = useState(false);

  const [brandImageFile, setBrandImageFile] = useState<File | null>(null);
  const [brandImagePreview, setBrandImagePreview] = useState<string | null>(initialData?.brand?.image || null);
  const [brandImageUploading, setBrandImageUploading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ExtendedSchoolFormData>({
    resolver: zodResolver(extendedSchoolFormSchema),
    defaultValues: {
      name: initialData?.name || '',
      address: initialData?.address || '',
      phoneNumber: initialData?.phoneNumber || '',
      email: initialData?.email || '',
      registeredNumber: initialData?.registeredNumber || '',
      // Brand defaults
      color: initialData?.brand?.color || '#3B82F6',
    }
  });

  // File validation helper
  const validateFile = (file: File): string | null => {
    if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
      return 'Please select a valid image file (JPEG, PNG, or SVG)';
    }
    if (file.size > MAX_FILE_SIZE) {
      return 'File size must be less than 2MB';
    }
    return null;
  };

  // Logo upload handler
  const handleLogoUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const validationError = validateFile(file);
    if (validationError) {
      setErrorMessage(validationError);
      return;
    }

    setLogoFile(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setLogoPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    setErrorMessage(null);
  }, []);

  // Brand image upload handler
  const handleBrandImageUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const validationError = validateFile(file);
    if (validationError) {
      setErrorMessage(validationError);
      return;
    }

    setBrandImageFile(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setBrandImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    setErrorMessage(null);
  }, []);

  // Clear handlers
  const handleClearLogo = useCallback(() => {
    setLogoFile(null);
    setLogoPreview(null);
  }, []);

  const handleClearBrandImage = useCallback(() => {
    setBrandImageFile(null);
    setBrandImagePreview(null);
  }, []);

  const onSubmit: SubmitHandler<ExtendedSchoolFormData> = async (data) => {
    setIsSubmitting(true);
    setSuccessMessage(null);
    setErrorMessage(null);

    try {
      let schoolResult;
      let brandResult;
      let logoUrl: string | undefined;
      let brandImageUrl: string | undefined;

      // Upload logo if provided
      if (logoFile) {
        setLogoUploading(true);
        const logoUploadResult = await handleFileUploadAction(logoFile);
        if (logoUploadResult.status === 'success' && logoUploadResult.data) {
          logoUrl = logoUploadResult.data.url;
        } else {
          throw new Error('Failed to upload logo');
        }
        setLogoUploading(false);
      } else if (logoPreview && mode === 'edit') {
        // Keep existing logo URL for edit mode
        logoUrl = logoPreview;
      }

      // Upload brand image if provided
      if (brandImageFile) {
        setBrandImageUploading(true);
        const imageUploadResult = await handleFileUploadAction(brandImageFile);
        if (imageUploadResult.status === 'success' && imageUploadResult.data) {
          brandImageUrl = imageUploadResult.data.url;
        } else {
          throw new Error('Failed to upload brand image');
        }
        setBrandImageUploading(false);
      } else if (brandImagePreview && mode === 'edit') {
        // Keep existing brand image URL for edit mode
        brandImageUrl = brandImagePreview;
      }

      // Handle brand creation/update
      const brandPayload = {
        logo: logoUrl || undefined,
        color: data.color || undefined,
        image: brandImageUrl || undefined,
      };

      // Only create/update brand if at least one field is provided
      const hasBrandData = brandPayload.logo || brandPayload.color || brandPayload.image;

      if (hasBrandData) {
        const existingBrand = initialData?.brand;

        if (mode === 'edit' && existingBrand?.id) {
          // Update existing brand
          brandResult = await handleUpdateBrandAction(existingBrand.id, brandPayload);
        } else {
          // Create new brand
          brandResult = await handleCreateBrandAction(brandPayload);
        }

        if (brandResult.status !== 'success') {
          throw new Error(brandResult.message as string || 'Failed to save brand information');
        }
      }

      // Prepare school payload
      const schoolPayload = {
        name: data.name,
        address: data.address || '',
        phoneNumber: data.phoneNumber || '',
        registeredNumber: data.registeredNumber || '',
        email: data.email || '',
        ...(brandResult?.data?.id && { brandId: brandResult.data.id }),
      };

      if (mode === 'create') {
        schoolResult = await handleCreateSchoolAction(schoolPayload);
      } else {
        // Update mode - convert object to FormData
        const formData = new FormData();
        formData.append('name', schoolPayload.name);
        formData.append('address', schoolPayload.address);
        formData.append('phoneNumber', schoolPayload.phoneNumber);
        formData.append('registeredNumber', schoolPayload.registeredNumber);
        formData.append('email', schoolPayload.email);
        if (schoolPayload.brandId) {
          formData.append('brandId', schoolPayload.brandId);
        }
        schoolResult = await updateMySchool(formData);
      }

      if (schoolResult.status === 'success') {
        setSuccessMessage(mode === 'create' ? 'School and brand created successfully!' : 'School and brand updated successfully!');
        reset();

        // Update session with the new school information (only for create mode)
        if (mode === 'create' && schoolResult.data && updateSession) {
          try {
            await updateSession({
              user: {
                schoolId: schoolResult.data.id,
                school: {
                  id: schoolResult.data.id,
                  name: schoolResult.data.name,
                  address: schoolResult.data.address,
                  phoneNumber: schoolResult.data.phoneNumber,
                  registeredNumber: schoolResult.data.registeredNumber,
                  email: schoolResult.data.email,
                  brand: schoolResult.data.brand,
                }
              }
            });

            // Small delay to ensure session is propagated
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Call success callback after session update
            if (onSuccess) {
              onSuccess();
            }
          } catch (error) {
            // Still call success callback even if session update fails
            if (onSuccess) {
              onSuccess();
            }
          }
        } else if (mode === 'edit' && schoolResult.data && updateSession) {
          // For edit mode, update session with the updated school data
          try {
            await updateSession({
              user: {
                school: {
                  id: schoolResult.data.id,
                  name: schoolResult.data.name,
                  address: schoolResult.data.address,
                  phoneNumber: schoolResult.data.phoneNumber,
                  registeredNumber: schoolResult.data.registeredNumber,
                  email: schoolResult.data.email,
                  brand: schoolResult.data.brand,
                }
              }
            });

            // Call success callback after session update
            if (onSuccess) {
              onSuccess();
            }
          } catch (error) {
            // Still call success callback even if session update fails
            if (onSuccess) {
              onSuccess();
            }
          }
        } else {
          // Call success callback if no session update needed
          if (onSuccess) {
            onSuccess();
          }
        }
      } else {
        // Handle error message - can be string or array of validation errors
        let errorMsg: string;
        if (Array.isArray(schoolResult.message)) {
          errorMsg = schoolResult.message.map(err => err.constraints).join(', ');
        } else {
          errorMsg = schoolResult.message || 'Failed to save school information';
        }
        setErrorMessage(errorMsg);
        if (onError) {
          onError(errorMsg);
        }
      }
    } catch (error) {
      const errorMsg = 'An unexpected error occurred while creating the school';
      setErrorMessage(errorMsg);
      if (onError) {
        onError(errorMsg);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={cn('w-full max-w-6xl mx-auto', className)}>
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          {mode === 'create' ? 'Create Your School' : 'Update School Information'}
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          {mode === 'create'
            ? 'Set up your educational institution and brand identity to start managing students, teachers, and academic content.'
            : 'Update your school details and brand information.'
          }
        </p>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="alert alert-success mb-6">
          <Icon variant="check-circle" className="shrink-0" size={6} />
          <span>{successMessage}</span>
        </div>
      )}

      {/* Error Message */}
      {errorMessage && (
        <div className="alert alert-error mb-6">
          <Icon variant="x-circle" className="shrink-0" size={6} />
          <span>{errorMessage}</span>
        </div>
      )}

      {/* Form with Cards Layout */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* School Information Card */}
          <div className="card bg-base-100 shadow-lg border border-base-300">
            <div className="card-body">
              <h3 className="card-title text-xl mb-4 flex items-center gap-2">
                <Building2 className="w-5 h-5 text-primary" />
                School Information
              </h3>

              <div className="space-y-4">
                {/* School Name - Required */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium">School Name *</span>
                  </label>
                  <input
                    type="text"
                    placeholder="Enter your school name"
                    className={cn(
                      'input input-bordered w-full',
                      errors.name && 'input-error'
                    )}
                    {...register('name')}
                  />
                  {errors.name && (
                    <label className="label">
                      <span className="label-text-alt text-error">{errors.name.message}</span>
                    </label>
                  )}
                </div>

                {/* Address - Optional */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-primary" />
                      Address
                    </span>
                  </label>
                  <textarea
                    placeholder="Enter your school address"
                    className={cn(
                      'textarea textarea-bordered w-full',
                      errors.address && 'textarea-error'
                    )}
                    rows={3}
                    {...register('address')}
                  />
                  {errors.address && (
                    <label className="label">
                      <span className="label-text-alt text-error">{errors.address.message}</span>
                    </label>
                  )}
                </div>

                {/* Phone Number - Optional */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium flex items-center gap-2">
                      <Phone className="w-4 h-4 text-primary" />
                      Phone Number
                    </span>
                  </label>
                  <input
                    type="tel"
                    placeholder="Enter phone number"
                    className={cn(
                      'input input-bordered w-full',
                      errors.phoneNumber && 'input-error'
                    )}
                    {...register('phoneNumber')}
                  />
                  {errors.phoneNumber && (
                    <label className="label">
                      <span className="label-text-alt text-error">{errors.phoneNumber.message}</span>
                    </label>
                  )}
                </div>

                {/* Email - Optional */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium flex items-center gap-2">
                      <Mail className="w-4 h-4 text-primary" />
                      Email
                    </span>
                  </label>
                  <input
                    type="email"
                    placeholder="Enter school email"
                    className={cn(
                      'input input-bordered w-full',
                      errors.email && 'input-error'
                    )}
                    {...register('email')}
                  />
                  {errors.email && (
                    <label className="label">
                      <span className="label-text-alt text-error">{errors.email.message}</span>
                    </label>
                  )}
                </div>

                {/* Registration Number - Optional */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium flex items-center gap-2">
                      <FileText className="w-4 h-4 text-primary" />
                      Registration Number
                    </span>
                  </label>
                  <input
                    type="text"
                    placeholder="Enter registration number (if applicable)"
                    className={cn(
                      'input input-bordered w-full',
                      errors.registeredNumber && 'input-error'
                    )}
                    {...register('registeredNumber')}
                  />
                  {errors.registeredNumber && (
                    <label className="label">
                      <span className="label-text-alt text-error">{errors.registeredNumber.message}</span>
                    </label>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Brand Information Card */}
          <div className="card bg-base-100 shadow-lg border border-base-300">
            <div className="card-body">
              <h3 className="card-title text-xl mb-4 flex items-center gap-2">
                <Palette className="w-5 h-5 text-primary" />
                Brand Identity
              </h3>

              <div className="space-y-6">
                {/* Logo Upload Section */}
                <div className="space-y-3">
                  <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <Upload className="w-4 h-4 text-primary" />
                    School Logo
                  </h4>
                  <p className="text-sm text-gray-600">Upload your school logo (optional)</p>

                  <FileUpload
                    id="logo-upload"
                    accept="image/*"
                    preview={logoPreview}
                    onUpload={handleLogoUpload}
                    onClear={handleClearLogo}
                    label="Click to upload logo"
                    maxSize="MAX. 2MB"
                    disabled={logoUploading || isSubmitting}
                  />

                  {logoUploading && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span className="loading loading-spinner loading-sm"></span>
                      <span>Uploading logo...</span>
                    </div>
                  )}
                </div>

                {/* Brand Image Upload Section */}
                <div className="space-y-3">
                  <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <Upload className="w-4 h-4 text-primary" />
                    Brand Image
                  </h4>
                  <p className="text-sm text-gray-600">Upload a brand image or banner (optional)</p>

                  <FileUpload
                    id="brand-image-upload"
                    accept="image/*"
                    preview={brandImagePreview}
                    onUpload={handleBrandImageUpload}
                    onClear={handleClearBrandImage}
                    label="Click to upload brand image"
                    maxSize="MAX. 2MB"
                    disabled={brandImageUploading || isSubmitting}
                  />

                  {brandImageUploading && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span className="loading loading-spinner loading-sm"></span>
                      <span>Uploading brand image...</span>
                    </div>
                  )}
                </div>

                {/* Brand Color Section */}
                <div className="space-y-3">
                  <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <Palette className="w-4 h-4 text-primary" />
                    Brand Color
                  </h4>
                  <p className="text-sm text-gray-600">Choose your school's primary brand color</p>

                  <div className="flex items-center gap-4">
                    <input
                      type="color"
                      className={cn(
                        'w-16 h-12 rounded-lg border-2 border-gray-300 cursor-pointer shadow-sm',
                        errors.color && 'border-error'
                      )}
                      {...register('color')}
                    />
                    <div className="flex-1">
                      <input
                        type="text"
                        placeholder="#3B82F6"
                        className={cn(
                          'input input-bordered w-full',
                          errors.color && 'input-error'
                        )}
                        {...register('color')}
                      />
                      {errors.color && (
                        <label className="label">
                          <span className="label-text-alt text-error">{errors.color.message}</span>
                        </label>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="form-control">
          <Button
            type="submit"
            variant="primary"
            className="w-full h-12 text-base font-semibold"
            isLoading={isSubmitting || logoUploading || brandImageUploading}
            disabled={isSubmitting || logoUploading || brandImageUploading}
            iconProps={{
              variant: 'arrow-right'
            }}
          >
            {isSubmitting || logoUploading || brandImageUploading
              ? (mode === 'create' ? 'Creating School & Brand...' : 'Updating School Information...')
              : (mode === 'create' ? 'Create School & Brand' : 'Update School Information')
            }
          </Button>
        </div>
      </form>
    </div>
  );
};

export default IntegratedSchoolSetup;
